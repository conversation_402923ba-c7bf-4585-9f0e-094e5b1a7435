/* 菱形节点样式 */
.diamondNode {
  position: relative;
  width: 160px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.diamondNode:hover {
  transform: scale(1.05);
}

.diamondNode.selected {
  filter: drop-shadow(0 0 10px rgba(2, 119, 189, 0.5));
}

.diamondSvg {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.diamondShape {
  transition: all 0.2s ease;
}

/* 弱关系双边框样式 */
.weakRelationship .diamondShape {
  stroke-width: 2;
}

/* 弱关系内部边框 */
.weakRelationship .innerDiamondShape {
  fill: none;
  stroke: #0277bd;
  stroke-width: 2;
}

.diamondNode:hover .diamondShape {
  fill: #b3e5fc;
  stroke: #01579b;
  stroke-width: 3;
}

.diamondNode.weakRelationship:hover .diamondShape {
  fill: #b3e5fc;
  stroke: #01579b;
  stroke-width: 2;
}

.diamondNode.selected .diamondShape {
  fill: #81d4fa;
  stroke: #2877b4;
  stroke-width: 3;
}

.diamondNode.weakRelationship.selected .diamondShape {
  fill: #81d4fa;
  stroke: #1973b8;
  stroke-width: 2;
}

/* 节点悬停效果 */
.diamondNode:hover {
  transform: translateY(-1px);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
  transition: all 0.2s ease;
}

.labelContainer {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 120px;
  padding: 0 10px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.labelContainer:hover {
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 4px;
  transform: scale(1.05);
  transition: all 0.2s ease;
}

.label {
  font-size: 14px;
  font-weight: 600;
  color: #01579b;
  line-height: 1.2;
  margin-bottom: 2px;
  word-wrap: break-word;
}

.description {
  font-size: 10px;
  color: #0277bd;
  line-height: 1.1;
  opacity: 0.8;
  word-wrap: break-word;
}

.attributesContainer {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 160px;
  max-width: 250px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  margin-top: 5px;
}

.diamondNode:hover .attributesContainer {
  opacity: 1;
  visibility: visible;
}

.relationDescription {
  font-size: 12px;
  color: #0277bd;
  font-weight: 500;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
  line-height: 1.3;
}

.attributesList {
  margin-top: 8px;
}

.attributesTitle {
  font-size: 11px;
  color: #666;
  font-weight: 600;
  margin-bottom: 6px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.attribute {
  font-size: 11px;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.2;
}

.attribute:last-child {
  margin-bottom: 0;
}

.attributeName {
  font-weight: 500;
  color: #0277bd;
}

.attributeType {
  color: #666;
  font-style: italic;
}

/* 内联编辑器样式 */
.inlineEditor {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  min-width: 100px;
  max-width: 150px;
}

.handle {
  width: 4px !important;
  height: 4px !important;
  background: #2196f3 !important;
  border: 2px solid white !important;
  border-radius: 50% !important;
  transition: all 0.2s ease !important;
  opacity: 0;
  /* transform: scale(0.9); */
  /* 增加可点击区域 */
  padding: 4px !important;
  margin: -0px !important;
}

.diamondNode:hover .handle {
  opacity: 1;
  transform: scale(1);
  z-index: 10;
}

.handle:hover {
  width: 4px !important;
  height: 4px !important;
  /* hover 时增大可点击区域 */
  padding: 6px !important;
  margin: 0px !important;
  box-shadow: 0 0 8px rgba(61, 152, 212, 0.5) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .diamondNode {
    width: 120px;
    height: 80px;
  }

  .label {
    font-size: 12px;
  }

  .description {
    font-size: 9px;
  }

  .attributesContainer {
    min-width: 100px;
  }

  .attribute {
    font-size: 10px;
  }
}
